#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标相似度过滤处理器

在 DetectionToJsonConverter 类的 _save_json 方法保存JSON数据之前，
实现坐标相似度过滤功能，通过与数据库中已存在的坐标数据进行对比，
过滤掉相似的坐标，只保留不相似的新坐标数据。

作者: AI Assistant
创建时间: 2025-01-21
版本: 1.0.0
"""

import json
import traceback
from typing import Dict, List, Optional, Any, Union

from global_tools.ui_tools import CheckBoxManager, LineEditManager, LogOutput
from global_tools.utils import Logger, Colors
from global_tools.postgre_sql import PostgreSQLClient
from anylabeling.customize_ui.src.ui_operate.save_data.coordinate_similarity_processor import CoordinateSimilarityProcessor


class CoordinateFilterProcessor:
    """
    坐标相似度过滤处理器

    在保存JSON数据之前，通过与数据库中已存在的坐标数据进行对比，
    过滤掉相似的坐标，只保留不相似的新坐标数据。

    核心功能：
    1. UI控件配置获取 - 获取模式选择、数据库配置、相似度阈值
    2. 数据库连接和查询 - 根据模式查询对应的数据列
    3. 坐标相似度对比 - 使用CoordinateSimilarityProcessor进行对比
    4. 坐标过滤逻辑 - 过滤相似坐标，保留不相似的新坐标

    使用示例:
        processor = CoordinateFilterProcessor(
            checkbox_manager=checkbox_manager,
            line_edit_manager=line_edit_manager,
            logger=logger,
            log_output=log_output
        )

        filtered_data = processor.filter_coordinates_before_save(json_data)
    """

    def __init__(
        self,
        checkbox_manager: CheckBoxManager,
        line_edit_manager: LineEditManager,
        logger: Logger,
        log_output: LogOutput
    ):
        """
        初始化坐标过滤处理器

        参数:
            checkbox_manager: CheckBoxManager实例，管理QCheckBox控件状态
            line_edit_manager: LineEditManager实例，管理QLineEdit控件状态
            logger: Logger实例，控制台日志输出
            log_output: LogOutput实例，UI界面日志输出
        """
        self.__checkbox_manager = checkbox_manager
        self.__line_edit_manager = line_edit_manager
        self.__logger = logger
        self.__log_output = log_output

        # UI控件配置映射
        self.__UI_WIDGET_CONFIG = {
            # 模式选择控件
            "mode_selection": {
                "segment_checkbox": "rotate_146",  # segment模式复选框
                "obb_checkbox": "rotate_147"       # obb模式复选框
            },
            # 数据库连接配置控件
            "database_config": {
                "database_name": "lineEdit_174",      # 数据库名称
                "table_name": "lineEdit_175",         # 数据库表名
                "filter_condition": "lineEdit_176"    # 数据筛选条件
            },
            # 相似度阈值控件
            "similarity_config": {
                "threshold": "lineEdit_81"  # CoordinateSimilarityProcessor类的相似度阈值
            }
        }

    def __get_mode_selection(self) -> str:
        """
        获取当前选择的模式

        返回:
            str: 'segment' 或 'obb'，默认为'segment'
        """
        try:
            # 检查segment模式
            segment_checked = self.__checkbox_manager.get_checked_state_by_object_name(
                self.__UI_WIDGET_CONFIG["mode_selection"]["segment_checkbox"]
            )

            # 检查obb模式
            obb_checked = self.__checkbox_manager.get_checked_state_by_object_name(
                self.__UI_WIDGET_CONFIG["mode_selection"]["obb_checkbox"]
            )

            # 优先级：obb > segment > 默认segment
            if obb_checked is True:
                return "obb"
            elif segment_checked is True:
                return "segment"
            else:
                return "segment"  # 默认模式

        except Exception as e:
            self.__logger.error(f"获取模式选择时出错: {e}")
            return "segment"  # 出错时返回默认模式

    def __get_database_config(self) -> Dict[str, str]:
        """
        获取数据库连接配置

        返回:
            Dict[str, str]: 包含数据库配置的字典
        """
        try:
            config = {}
            db_config = self.__UI_WIDGET_CONFIG["database_config"]

            config["database_name"] = self.__line_edit_manager.get_text(db_config["database_name"]) or ""
            config["table_name"] = self.__line_edit_manager.get_text(db_config["table_name"]) or ""
            config["filter_condition"] = self.__line_edit_manager.get_text(db_config["filter_condition"]) or ""

            return config

        except Exception as e:
            self.__logger.error(f"获取数据库配置时出错: {e}")
            return {"database_name": "", "table_name": "", "filter_condition": ""}

    def __get_similarity_threshold(self) -> float:
        """
        获取相似度阈值

        返回:
            float: 相似度阈值，默认为0.8
        """
        try:
            threshold_text = self.__line_edit_manager.get_text(
                self.__UI_WIDGET_CONFIG["similarity_config"]["threshold"]
            )

            if threshold_text:
                threshold = float(threshold_text)
                # 确保阈值在合理范围内
                return max(0.0, min(1.0, threshold))
            else:
                return 0.8  # 默认阈值

        except (ValueError, TypeError) as e:
            self.__logger.warning(f"解析相似度阈值时出错: {e}，使用默认值0.8")
            return 0.8

    def __query_database_coordinates(self, mode: str, image_path: str, db_config: Dict[str, str]) -> List[List[List[float]]]:
        """
        查询数据库中的坐标数据

        参数:
            mode: 模式 ('segment' 或 'obb')
            image_path: 图像路径
            db_config: 数据库配置

        返回:
            List[List[List[float]]]: 数据库中的坐标数据列表
        """
        coordinates_list = []

        try:
            # 检查数据库配置
            if not db_config["database_name"] or not db_config["table_name"]:
                self.__logger.warning("数据库配置不完整，跳过数据库查询")
                return coordinates_list

            # 创建数据库连接
            client = PostgreSQLClient(
                host="localhost",
                port=5432,
                database=db_config["database_name"],
                user="postgres",
                password="123456",
                min_connections=1,
                max_connections=5
            )

            # 确定查询的数据列
            data_column = "segmentation_data" if mode == "segment" else "obb_data"

            # 构建查询条件
            condition_parts = [f"image_id = '{image_path}'", f"data_source = '{data_column}'"]
            if db_config["filter_condition"]:
                condition_parts.append(db_config["filter_condition"])

            condition_str = " AND ".join(condition_parts)

            # 执行查询
            results = client.fetch_data(db_config["table_name"], condition_str)

            # 提取坐标数据
            for row in results:
                if data_column in row and row[data_column]:
                    data = row[data_column]
                    if isinstance(data, (list, tuple)):
                        for item in data:
                            if isinstance(item, dict) and "points" in item:
                                coordinates_list.append(item["points"])

            client.close()

            self.__logger.info(f"从数据库查询到 {len(coordinates_list)} 个坐标数据")

        except Exception as e:
            self.__logger.error(f"查询数据库坐标时出错: {e}")
            self.__logger.error(traceback.format_exc())

        return coordinates_list

    def __compare_coordinates_similarity(self, current_points: List[List[float]],
                                       db_coordinates: List[List[List[float]]],
                                       threshold: float) -> bool:
        """
        比较当前坐标与数据库坐标的相似度

        参数:
            current_points: 当前坐标点
            db_coordinates: 数据库中的坐标列表
            threshold: 相似度阈值

        返回:
            bool: True表示找到相似坐标（应该跳过），False表示没有相似坐标（应该保留）
        """
        try:
            # 创建坐标相似度处理器
            similarity_processor = CoordinateSimilarityProcessor(
                logger=self.__logger,
                log_output=self.__log_output,
                mode='strict'  # 使用严格模式
            )

            # 与数据库中的每个坐标进行比较
            for db_points in db_coordinates:
                try:
                    result = similarity_processor.compare_coordinates(
                        points1=current_points,
                        points2=db_points,
                        similarity_threshold=threshold
                    )

                    # 如果相似度超过阈值，认为是相似坐标
                    if result.get('decision') == 'skip':
                        self.__logger.debug(f"发现相似坐标，跳过: {result.get('reason', '')}")
                        return True  # 找到相似坐标，应该跳过

                except Exception as e:
                    self.__logger.warning(f"比较坐标相似度时出错: {e}")
                    continue

            return False  # 没有找到相似坐标，应该保留

        except Exception as e:
            self.__logger.error(f"坐标相似度比较过程出错: {e}")
            return False  # 出错时保守处理，保留坐标

    def filter_coordinates_before_save(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        在保存JSON数据之前过滤相似的坐标

        参数:
            json_data: 要保存的JSON数据

        返回:
            Dict[str, Any]: 过滤后的JSON数据
        """
        try:
            # 获取配置
            mode = self.__get_mode_selection()
            db_config = self.__get_database_config()
            threshold = self.__get_similarity_threshold()

            self.__logger.info(f"开始坐标过滤 - 模式: {mode}, 阈值: {threshold}")

            # 检查JSON数据格式
            if not isinstance(json_data, dict) or "shapes" not in json_data:
                self.__logger.warning("JSON数据格式不正确，跳过坐标过滤")
                return json_data

            # 获取图像路径
            image_path = json_data.get("imagePath", "")
            if not image_path:
                self.__logger.warning("未找到图像路径，跳过坐标过滤")
                return json_data

            # 查询数据库中的坐标数据
            db_coordinates = self.__query_database_coordinates(mode, image_path, db_config)

            if not db_coordinates:
                self.__logger.info("数据库中没有找到相关坐标数据，保留所有坐标")
                return json_data

            # 过滤相似坐标
            original_shapes = json_data["shapes"]
            filtered_shapes = []
            skipped_count = 0

            for shape in original_shapes:
                if "points" in shape and shape["points"]:
                    # 检查当前坐标是否与数据库中的坐标相似
                    is_similar = self.__compare_coordinates_similarity(
                        shape["points"], db_coordinates, threshold
                    )

                    if is_similar:
                        skipped_count += 1
                        self.__logger.debug(f"跳过相似坐标: {shape.get('label', 'unknown')}")
                    else:
                        filtered_shapes.append(shape)
                else:
                    # 没有坐标信息的shape直接保留
                    filtered_shapes.append(shape)

            # 更新JSON数据
            json_data["shapes"] = filtered_shapes

            # 记录过滤结果
            self.__logger.info(f"坐标过滤完成 - 原始: {len(original_shapes)}, 过滤后: {len(filtered_shapes)}, 跳过: {skipped_count}")

            if self.__log_output:
                self.__log_output.append(
                    f"坐标过滤完成 - 原始: {len(original_shapes)}, 保留: {len(filtered_shapes)}, 跳过相似: {skipped_count}",
                    color=Colors.INFO
                )

            return json_data

        except Exception as e:
            self.__logger.error(f"坐标过滤过程出错: {e}")
            self.__logger.error(traceback.format_exc())

            if self.__log_output:
                self.__log_output.append(f"坐标过滤失败: {e}", color=Colors.ERROR)

            # 出错时返回原始数据，确保不影响正常流程
            return json_data

