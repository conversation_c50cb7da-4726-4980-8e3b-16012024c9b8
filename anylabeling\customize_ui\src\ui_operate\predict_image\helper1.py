#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能坐标去重过滤器

该模块实现了一个智能坐标去重过滤器类，用于在保存JSON标注数据前过滤掉与数据库中已存在的相似坐标。
通过配置化方式获取UI控件值，查询数据库中的坐标数据，使用CoordinateSimilarityProcessor进行相似度对比，
实现智能去重功能。

主要功能：
- 配置化UI控件管理
- 数据库坐标查询
- 坐标相似度对比
- 智能去重决策

作者: AI Assistant
创建时间: 2025-01-21
版本: 1.0.0
"""

import json
import traceback
from typing import Dict, List, Any, Optional, Union

from global_tools.utils import Logger, Colors
from global_tools.ui_tools.compnonent.qcheckbox import CheckBoxManager
from global_tools.ui_tools.compnonent.qline_edit import LineEditManager
from global_tools.ui_tools.compnonent.log_output import LogOutput
from anylabeling.customize_ui.src.ui_operate.save_data.coordinate_similarity_processor import CoordinateSimilarityProcessor
from anylabeling.customize_ui.src.ui_operate.save_data.postgre_sql import PostgreSQLClient


class IntelligentCoordinateDeduplicator:
    """
    智能坐标去重过滤器类

    用于在保存JSON标注数据前过滤掉与数据库中已存在的相似坐标。
    通过配置化方式获取UI控件值，避免硬编码，支持segment和obb两种模式。

    主要特性：
    - 配置化UI控件管理：通过CheckBoxManager和LineEditManager获取UI配置
    - 智能模式检测：根据UI状态自动判断segment/obb模式
    - 数据库查询：查询对应模式的坐标数据进行对比
    - 相似度计算：使用CoordinateSimilarityProcessor进行精确相似度计算
    - 智能决策：基于相似度阈值进行去重决策

    使用示例：
        deduplicator = IntelligentCoordinateDeduplicator(
            checkbox_manager=checkbox_manager,
            lineedit_manager=lineedit_manager,
            logger=logger,
            log_output=log_output
        )

        filtered_data = deduplicator.process_json_data(json_data)
        if filtered_data:
            # 保存过滤后的数据
            pass
    """

    def __init__(
        self,
        checkbox_manager: CheckBoxManager,
        lineedit_manager: LineEditManager,
        logger: Logger,
        log_output: LogOutput
    ):
        """
        初始化智能坐标去重过滤器

        参数:
            checkbox_manager: CheckBoxManager实例，用于管理QCheckBox控件状态
            lineedit_manager: LineEditManager实例，用于管理QLineEdit控件状态
            logger: Logger实例，用于控制台日志输出
            log_output: LogOutput实例，用于UI界面重要日志信息输出
        """
        self.__checkbox_manager = checkbox_manager
        self.__lineedit_manager = lineedit_manager
        self.__logger = logger
        self.__log_output = log_output

        # 数据库客户端实例
        self.__db_client: Optional[PostgreSQLClient] = None

        # 坐标相似度处理器
        self.__similarity_processor: Optional[CoordinateSimilarityProcessor] = None

        # UI控件配置缓存
        self.__ui_config: Optional[Dict[str, Any]] = None

        self.__logger.info("智能坐标去重过滤器初始化完成")
        if self.__log_output:
            self.__log_output.append("智能坐标去重过滤器初始化完成", Colors.GREEN)

    def __get_ui_configurations(self) -> Dict[str, Any]:
        """
        获取所有UI控件配置值（避免硬编码）

        返回:
            包含所有UI配置的字典
            {
                'segment_mode': bool,           # rotate_146选中状态
                'obb_mode': bool,              # rotate_147选中状态
                'database_name': str,          # lineEdit_174数据库名称
                'table_name': str,             # lineEdit_175数据库表名
                'filter_condition': str,       # lineEdit_176数据库筛选条件
                'similarity_threshold': float  # lineEdit_81相似度阈值
            }
        """
        try:
            # 获取CheckBox状态
            segment_mode = self.__checkbox_manager.get_checked_state_by_object_name("rotate_146") or False
            obb_mode = self.__checkbox_manager.get_checked_state_by_object_name("rotate_147") or False

            # 获取LineEdit值
            database_name = self.__lineedit_manager.get_text("lineEdit_174") or ""
            table_name = self.__lineedit_manager.get_text("lineEdit_175") or ""
            filter_condition = self.__lineedit_manager.get_text("lineEdit_176") or ""

            # 获取相似度阈值，默认0.30
            similarity_threshold_str = self.__lineedit_manager.get_text("lineEdit_81") or "0.30"
            try:
                similarity_threshold = float(similarity_threshold_str)
                # 确保阈值在合理范围内
                if not 0.0 <= similarity_threshold <= 1.0:
                    similarity_threshold = 0.30
                    self.__logger.warning(f"相似度阈值超出范围，使用默认值0.30")
            except ValueError:
                similarity_threshold = 0.30
                self.__logger.warning(f"相似度阈值格式错误，使用默认值0.30")

            config = {
                'segment_mode': segment_mode,
                'obb_mode': obb_mode,
                'database_name': database_name.strip(),
                'table_name': table_name.strip(),
                'filter_condition': filter_condition.strip(),
                'similarity_threshold': similarity_threshold
            }

            self.__logger.debug(f"UI配置获取成功: {config}")
            return config

        except Exception as e:
            self.__logger.error(f"获取UI配置失败: {str(e)}")
            self.__logger.error(traceback.format_exc())
            if self.__log_output:
                self.__log_output.append(f"获取UI配置失败: {str(e)}", Colors.ERROR)

            # 返回默认配置
            return {
                'segment_mode': False,
                'obb_mode': False,
                'database_name': "",
                'table_name': "",
                'filter_condition': "",
                'similarity_threshold': 0.30
            }

    def __determine_data_mode(self) -> str:
        """
        根据UI状态确定数据模式

        返回:
            'segment': 分割模式
            'obb': OBB模式
            'none': 未选择任何模式
        """
        config = self.__get_ui_configurations()

        if config['segment_mode']:
            return 'segment'
        elif config['obb_mode']:
            return 'obb'
        else:
            return 'none'

    def __initialize_database_connection(self) -> bool:
        """
        初始化数据库连接

        返回:
            连接成功返回True，否则返回False
        """
        try:
            config = self.__get_ui_configurations()

            if not config['database_name']:
                self.__logger.warning("数据库名称为空，跳过数据库连接")
                return False

            if not config['table_name']:
                self.__logger.warning("数据库表名为空，跳过数据库连接")
                return False

            # 创建数据库连接
            self.__db_client = PostgreSQLClient(
                host="localhost",
                port=5432,
                database=config['database_name'],
                user="postgres",
                password="123456",
                min_connections=1,
                max_connections=5
            )

            self.__logger.info(f"成功连接到数据库: {config['database_name']}")
            if self.__log_output:
                self.__log_output.append(f"成功连接到数据库: {config['database_name']}", Colors.GREEN)

            return True

        except Exception as e:
            self.__logger.error(f"数据库连接失败: {str(e)}")
            self.__logger.error(traceback.format_exc())
            if self.__log_output:
                self.__log_output.append(f"数据库连接失败: {str(e)}", Colors.ERROR)
            return False

    def __initialize_similarity_processor(self) -> bool:
        """
        初始化坐标相似度处理器

        返回:
            初始化成功返回True，否则返回False
        """
        try:
            self.__similarity_processor = CoordinateSimilarityProcessor(
                logger=self.__logger,
                log_output=self.__log_output,
                mode='strict'  # 使用严格模式
            )

            self.__logger.info("坐标相似度处理器初始化成功")
            return True

        except Exception as e:
            self.__logger.error(f"坐标相似度处理器初始化失败: {str(e)}")
            self.__logger.error(traceback.format_exc())
            if self.__log_output:
                self.__log_output.append(f"坐标相似度处理器初始化失败: {str(e)}", Colors.ERROR)
            return False

    def __query_database_coordinates(self, data_mode: str) -> List[List[List[float]]]:
        """
        查询数据库中的坐标数据

        参数:
            data_mode: 数据模式 ('segment' 或 'obb')

        返回:
            坐标数据列表，每个元素为一个坐标集合 [[x1,y1], [x2,y2], ...]
        """
        try:
            if not self.__db_client:
                self.__logger.warning("数据库客户端未初始化")
                return []

            config = self.__get_ui_configurations()
            table_name = config['table_name']
            filter_condition = config['filter_condition']

            # 根据模式确定查询的列
            if data_mode == 'segment':
                column_name = 'segmentation_data'
            elif data_mode == 'obb':
                column_name = 'obb_data'
            else:
                self.__logger.warning(f"不支持的数据模式: {data_mode}")
                return []

            # 构建查询条件
            condition_str = None
            if filter_condition:
                condition_str = filter_condition

            # 查询数据库
            result = self.__db_client.fetch_data(
                table_name=table_name,
                condition_str=condition_str,
                columns=[column_name]
            )

            coordinates_list = []

            if result and isinstance(result, list):
                for row in result:
                    if isinstance(row, dict) and column_name in row:
                        coordinate_data = row[column_name]

                        # 处理JSONB数据
                        if isinstance(coordinate_data, str):
                            try:
                                coordinate_data = json.loads(coordinate_data)
                            except json.JSONDecodeError:
                                continue

                        # 提取坐标点
                        if isinstance(coordinate_data, list):
                            for coord_item in coordinate_data:
                                if isinstance(coord_item, list) and len(coord_item) >= 2:
                                    # 确保坐标格式为 [[x1,y1], [x2,y2], ...]
                                    if all(isinstance(point, list) and len(point) >= 2 for point in coord_item):
                                        coordinates_list.append(coord_item)

            self.__logger.info(f"从数据库查询到 {len(coordinates_list)} 个坐标集合")
            return coordinates_list

        except Exception as e:
            self.__logger.error(f"查询数据库坐标失败: {str(e)}")
            self.__logger.error(traceback.format_exc())
            if self.__log_output:
                self.__log_output.append(f"查询数据库坐标失败: {str(e)}", Colors.ERROR)
            return []

    def __extract_coordinates_from_json(self, json_data: Dict[str, Any], data_mode: str) -> List[List[List[float]]]:
        """
        从JSON数据中提取坐标

        参数:
            json_data: JSON标注数据
            data_mode: 数据模式 ('segment' 或 'obb')

        返回:
            坐标数据列表，每个元素为一个坐标集合 [[x1,y1], [x2,y2], ...]
        """
        try:
            coordinates_list = []

            if not isinstance(json_data, dict) or 'shapes' not in json_data:
                return coordinates_list

            shapes = json_data.get('shapes', [])
            if not isinstance(shapes, list):
                return coordinates_list

            for shape in shapes:
                if not isinstance(shape, dict):
                    continue

                # 获取坐标点
                points = shape.get('points', [])
                if not isinstance(points, list) or len(points) < 3:
                    continue

                # 确保坐标格式正确
                if all(isinstance(point, list) and len(point) >= 2 for point in points):
                    # 转换为浮点数格式
                    float_points = [[float(point[0]), float(point[1])] for point in points]
                    coordinates_list.append(float_points)

            self.__logger.debug(f"从JSON数据中提取到 {len(coordinates_list)} 个坐标集合")
            return coordinates_list

        except Exception as e:
            self.__logger.error(f"从JSON数据提取坐标失败: {str(e)}")
            self.__logger.error(traceback.format_exc())
            return []

    def __is_coordinate_similar(self, new_coord: List[List[float]], existing_coords: List[List[List[float]]], similarity_threshold: float) -> bool:
        """
        检查新坐标是否与已存在的坐标相似

        参数:
            new_coord: 新的坐标集合 [[x1,y1], [x2,y2], ...]
            existing_coords: 已存在的坐标集合列表
            similarity_threshold: 相似度阈值

        返回:
            如果相似返回True，否则返回False
        """
        try:
            if not self.__similarity_processor:
                self.__logger.warning("坐标相似度处理器未初始化")
                return False

            for existing_coord in existing_coords:
                # 使用CoordinateSimilarityProcessor进行相似度计算
                result = self.__similarity_processor.compare_coordinates(
                    points1=new_coord,
                    points2=existing_coord,
                    similarity_threshold=similarity_threshold
                )

                # 检查决策结果
                if result.get('decision') == 'skip':
                    self.__logger.debug(f"发现相似坐标，跳过: {result.get('reason', '未知原因')}")
                    return True

            return False

        except Exception as e:
            self.__logger.error(f"坐标相似度检查失败: {str(e)}")
            self.__logger.error(traceback.format_exc())
            # 出现异常时保守处理，不跳过
            return False

    def __filter_json_coordinates(self, json_data: Dict[str, Any], data_mode: str, existing_coords: List[List[List[float]]], similarity_threshold: float) -> Dict[str, Any]:
        """
        过滤JSON数据中的相似坐标

        参数:
            json_data: 原始JSON数据
            data_mode: 数据模式
            existing_coords: 已存在的坐标集合列表
            similarity_threshold: 相似度阈值

        返回:
            过滤后的JSON数据
        """
        try:
            if not isinstance(json_data, dict) or 'shapes' not in json_data:
                return json_data

            original_shapes = json_data.get('shapes', [])
            filtered_shapes = []

            skipped_count = 0
            kept_count = 0

            for shape in original_shapes:
                if not isinstance(shape, dict):
                    continue

                # 获取坐标点
                points = shape.get('points', [])
                if not isinstance(points, list) or len(points) < 3:
                    filtered_shapes.append(shape)
                    kept_count += 1
                    continue

                # 转换为浮点数格式
                try:
                    float_points = [[float(point[0]), float(point[1])] for point in points]
                except (ValueError, IndexError, TypeError):
                    # 坐标格式错误，保留原样
                    filtered_shapes.append(shape)
                    kept_count += 1
                    continue

                # 检查是否与已存在坐标相似
                if self.__is_coordinate_similar(float_points, existing_coords, similarity_threshold):
                    skipped_count += 1
                    self.__logger.debug(f"跳过相似坐标: {shape.get('label', '未知标签')}")
                else:
                    filtered_shapes.append(shape)
                    kept_count += 1

            # 创建过滤后的JSON数据
            filtered_json = json_data.copy()
            filtered_json['shapes'] = filtered_shapes

            self.__logger.info(f"坐标过滤完成: 保留 {kept_count} 个，跳过 {skipped_count} 个")
            if self.__log_output:
                self.__log_output.append(f"坐标过滤完成: 保留 {kept_count} 个，跳过 {skipped_count} 个", Colors.BLUE)

            return filtered_json

        except Exception as e:
            self.__logger.error(f"过滤JSON坐标失败: {str(e)}")
            self.__logger.error(traceback.format_exc())
            if self.__log_output:
                self.__log_output.append(f"过滤JSON坐标失败: {str(e)}", Colors.ERROR)
            # 出现异常时返回原始数据
            return json_data